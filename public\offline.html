<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - QRAnalytica</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #2C3E50 0%, #34495E 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            padding: 2rem;
            max-width: 400px;
        }
        .icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 2rem;
            background: #18BC9C;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
        }
        h1 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        p {
            opacity: 0.8;
            line-height: 1.6;
            margin-bottom: 2rem;
        }
        .retry-btn {
            background: #18BC9C;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .retry-btn:hover {
            background: #16A085;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">📊</div>
        <h1>You're Offline</h1>
        <p>It looks like you've lost your internet connection. Some features may be limited until you're back online.</p>
        <button class="retry-btn" onclick="window.location.reload()">Try Again</button>
    </div>
    
    <script>
        // Auto-retry when back online
        window.addEventListener('online', () => {
            window.location.reload();
        });
    </script>
</body>
</html>
