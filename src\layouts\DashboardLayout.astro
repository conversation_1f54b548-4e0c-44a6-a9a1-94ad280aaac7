---
import '../styles/global.css';
import Sidebar from '../components/Sidebar';
import CrispChat from '../components/CrispChat';
import TrialStatusChecker from '../components/dashboard/TrialStatusChecker';

export interface Props {
  title?: string;
}

const {
  title = 'QR Analytics Dashboard',
} = Astro.props as Props;

---

<!doctype html>
<html lang="en" class="h-full">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="description" content="QRAnalytica administration dashboard" />
    <title>{title}</title>

    <!-- PWA Meta Tags -->
    <meta name="application-name" content="QRAnalytica" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="QRAnalytica" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="msapplication-config" content="/browserconfig.xml" />
    <meta name="msapplication-TileColor" content="#2C3E50" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="theme-color" content="#18BC9C" />

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="/icons/icon-152x152.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="/icons/icon-152x152.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/icons/icon-192x192.png" />

    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="/icons/icon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/icons/icon-16x16.png" />

    <!-- Preload critical resources -->
    <link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossorigin />

    <!-- Performance optimizations -->
    <meta name="referrer" content="strict-origin-when-cross-origin" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  </head>
  <body class="h-full flex bg-gradient-to-br from-gray-50 to-gray-100 text-gray-900 antialiased overflow-x-hidden">
    <!-- Sidebar -->
    <aside
      id="sidebar"
      class="fixed inset-y-0 left-0 z-40 w-64 bg-gradient-to-b from-[#2C3E50] to-[#34495E] text-white transform -translate-x-full transition-all duration-300 ease-in-out md:translate-x-0 md:static md:inset-0 md:w-64 lg:w-72 shadow-2xl"
    >
      <Sidebar client:load />
    </aside>

    <!-- Overlay for mobile -->
    <div id="sidebar-overlay" class="md:hidden fixed inset-0 bg-black/60 opacity-0 pointer-events-none transition-opacity backdrop-blur-sm z-30"></div>

    <!-- Main content wrapper -->
    <div class="flex-1 flex flex-col min-h-screen w-full min-w-0">
      <!-- Enhanced Top bar -->
      <header class="bg-white/95 backdrop-blur-sm border-b border-gray-200/60 h-14 sm:h-16 lg:h-20 flex items-center px-3 sm:px-4 lg:px-6 xl:px-8 shadow-sm shrink-0">
        <div class="flex items-center justify-between w-full min-w-0">
          <!-- Left section: Mobile toggle + Title + Breadcrumbs -->
          <div class="flex items-center space-x-2 sm:space-x-3 lg:space-x-4 flex-1 min-w-0">
            <!-- Mobile toggle -->
            <button id="sidebarToggle" class="md:hidden p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-[#18BC9C] rounded-lg transition-colors touch-manipulation min-h-[44px] min-w-[44px] flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5m-16.5 5.25h16.5m-16.5 5.25h16.5" />
              </svg>
            </button>

            <!-- Title and Breadcrumbs -->
            <div class="flex flex-col min-w-0 flex-1">
              <h1 class="text-lg sm:text-xl font-bold text-gray-900 tracking-tight truncate">{title}</h1>
              <nav class="hidden sm:flex text-sm text-gray-500" aria-label="Breadcrumb">
                <ol class="flex items-center space-x-2">
                  <li><a href="/dashboard" class="hover:text-gray-700 transition-colors">Dashboard</a></li>
                  <li class="flex items-center">
                    <svg class="w-3 h-3 mx-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-gray-900 font-medium">Overview</span>
                  </li>
                </ol>
              </nav>
            </div>
          </div>

          <!-- Right section: Search + Notifications + Profile -->

        </div>
        <slot name="header" />
      </header>

      <!-- Page content -->
      <main class="flex-1 p-3 sm:p-4 lg:p-6 xl:p-8 overflow-y-auto bg-gradient-to-br from-white/50 to-gray-50/50 backdrop-blur-sm min-w-0">
        <div class="max-w-7xl mx-auto w-full min-w-0">
          <slot />
        </div>
      </main>
    </div>

    <!-- Trial Status Checker -->
    <TrialStatusChecker client:load />

    <!-- Crisp Chat Integration -->
    <CrispChat client:load />

    <script>
      // Enhanced dashboard interactions
      document.addEventListener('DOMContentLoaded', () => {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');
        const toggle = document.getElementById('sidebarToggle');
        const userMenuButton = document.getElementById('userMenuButton');
        const userDropdown = document.getElementById('userDropdown');

        // Sidebar functionality
        const openSidebar = () => {
          sidebar?.classList.remove('-translate-x-full');
          overlay?.classList.remove('opacity-0', 'pointer-events-none');
          overlay?.classList.add('opacity-100');
        };

        const closeSidebar = () => {
          sidebar?.classList.add('-translate-x-full');
          overlay?.classList.add('opacity-0');
          overlay?.classList.add('pointer-events-none');
        };

        toggle?.addEventListener('click', () => {
          if (sidebar?.classList.contains('-translate-x-full')) {
            openSidebar();
          } else {
            closeSidebar();
          }
        });

        overlay?.addEventListener('click', closeSidebar);

        // User dropdown functionality
        let isDropdownOpen = false;

        const toggleDropdown = () => {
          isDropdownOpen = !isDropdownOpen;
          if (isDropdownOpen) {
            userDropdown?.classList.remove('hidden');
            userDropdown?.classList.add('animate-in', 'fade-in-0', 'zoom-in-95');
          } else {
            userDropdown?.classList.add('hidden');
            userDropdown?.classList.remove('animate-in', 'fade-in-0', 'zoom-in-95');
          }
        };

        const closeDropdown = () => {
          if (isDropdownOpen) {
            isDropdownOpen = false;
            userDropdown?.classList.add('hidden');
            userDropdown?.classList.remove('animate-in', 'fade-in-0', 'zoom-in-95');
          }
        };

        userMenuButton?.addEventListener('click', (e) => {
          e.stopPropagation();
          toggleDropdown();
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
          const target = e.target as Element;
          if (!userMenuButton?.contains(target) && !userDropdown?.contains(target)) {
            closeDropdown();
          }
        });

        // Close dropdown on escape key
        document.addEventListener('keydown', (e) => {
          if (e.key === 'Escape') {
            closeDropdown();
          }
        });

        // Search functionality
        const searchInput = document.querySelector('input[placeholder="Search QR codes..."]') as HTMLInputElement;
        searchInput?.addEventListener('keydown', (e) => {
          if (e.key === 'Enter') {
            const query = searchInput.value.trim();
            if (query) {
              // Implement search functionality
              console.log('Searching for:', query);
              // You can redirect to search results or filter current view
            }
          }
        });
      });

      // PWA Service Worker Registration
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', async () => {
          try {
            const registration = await navigator.serviceWorker.register('/sw.js');
            console.log('Service Worker registered successfully:', registration);

            // Check for updates
            registration.addEventListener('updatefound', () => {
              const newWorker = registration.installing;
              if (newWorker) {
                newWorker.addEventListener('statechange', () => {
                  if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                    // New content is available, show update notification
                    showUpdateNotification();
                  }
                });
              }
            });
          } catch (error) {
            console.error('Service Worker registration failed:', error);
          }
        });
      }

      // PWA Install Prompt
      let deferredPrompt;
      window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;
        showInstallButton();
      });

      function showInstallButton() {
        // Create install button if it doesn't exist
        if (!document.getElementById('pwa-install-btn')) {
          const installBtn = document.createElement('button');
          installBtn.id = 'pwa-install-btn';
          installBtn.innerHTML = `
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Install App
          `;
          installBtn.className = 'fixed bottom-4 right-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-2 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center text-sm font-medium z-50 md:bottom-6 md:right-6';

          installBtn.addEventListener('click', async () => {
            if (deferredPrompt) {
              deferredPrompt.prompt();
              const { outcome } = await deferredPrompt.userChoice;
              console.log('PWA install outcome:', outcome);
              deferredPrompt = null;
              installBtn.remove();
            }
          });

          document.body.appendChild(installBtn);
        }
      }

      function showUpdateNotification() {
        // Create update notification
        const updateNotification = document.createElement('div');
        updateNotification.className = 'fixed top-4 right-4 bg-blue-600 text-white px-4 py-3 rounded-lg shadow-lg z-50 max-w-sm';
        updateNotification.innerHTML = `
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              <span class="text-sm font-medium">Update available</span>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-white/80 hover:text-white">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          <p class="text-xs text-white/90 mt-1">Refresh to get the latest features</p>
          <button onclick="window.location.reload()" class="mt-2 bg-white/20 hover:bg-white/30 text-white text-xs px-3 py-1 rounded transition-colors">
            Refresh Now
          </button>
        `;

        document.body.appendChild(updateNotification);

        // Auto-remove after 10 seconds
        setTimeout(() => {
          if (updateNotification.parentElement) {
            updateNotification.remove();
          }
        }, 10000);
      }

      // Handle online/offline status
      function updateOnlineStatus() {
        const statusIndicator = document.getElementById('online-status');
        if (navigator.onLine) {
          if (statusIndicator) statusIndicator.remove();
        } else {
          if (!statusIndicator) {
            const offlineIndicator = document.createElement('div');
            offlineIndicator.id = 'online-status';
            offlineIndicator.className = 'fixed top-0 left-0 right-0 bg-red-600 text-white text-center py-2 text-sm font-medium z-50';
            offlineIndicator.textContent = 'You are currently offline. Some features may be limited.';
            document.body.appendChild(offlineIndicator);
          }
        }
      }

      window.addEventListener('online', updateOnlineStatus);
      window.addEventListener('offline', updateOnlineStatus);
      updateOnlineStatus(); // Check initial status
    </script>
  </body>
</html>